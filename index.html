<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Warehouse Management Software - Flexe
  </title>
  <script src="https://cdn.tailwindcss.com">
  </script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet"/>
  <style>
   body {
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
    }

   /* Professional card shadows */
   .card-shadow {
     box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
   }

   .card-shadow-lg {
     box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
   }

   /* Smooth transitions */
   .transition-all {
     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
   }

   /* Professional gradient backgrounds */
   .bg-gradient-navy {
     background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%);
   }

   .bg-gradient-card {
     background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
   }

   /* Custom animations */
   @keyframes fadeInUp {
     from {
       opacity: 0;
       transform: translateY(30px);
     }
     to {
       opacity: 1;
       transform: translateY(0);
     }
   }

   .animate-fade-in-up {
     animation: fadeInUp 0.6s ease-out;
   }

   /* Professional hover effects */
   .hover-lift:hover {
     transform: translateY(-5px);
   }

   /* Modern glass effect */
   .glass-effect {
     background: rgba(255, 255, 255, 0.95);
     backdrop-filter: blur(10px);
     border: 1px solid rgba(255, 255, 255, 0.2);
   }
  </style>
 </head>
 <body class="bg-[#e3e8ef] text-[#0a1e3a]">
  <!-- Header Section -->
  <header class="relative overflow-hidden bg-gradient-to-r from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
   <div class="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 pt-6 pb-24 md:pb-32 relative z-10">
    <nav class="flex items-center justify-between py-4 mb-10">
     <!-- Logo with Icon -->
     <div class="flex items-center space-x-3">
      <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-[#4caf50] to-[#2e7d32] flex items-center justify-center shadow-lg">
       <i class="fas fa-warehouse text-white text-lg"></i>
      </div>
      <div class="text-white font-serif text-2xl font-bold select-none tracking-wide">
       Flexe
      </div>
     </div>

     <!-- Desktop Navigation -->
     <ul class="hidden lg:flex items-center space-x-8 text-sm">
      <li>
       <a class="text-white flex items-center space-x-2 font-semibold tracking-wide hover:text-[#4caf50] transition-colors duration-300" href="#">
        <span>Home</span>
        <i class="fas fa-chevron-right text-xs"></i>
       </a>
      </li>
      <li>
       <a class="text-[#a0b0c0] hover:text-white transition-colors duration-300 tracking-wide font-medium" href="#">
        Pricing
       </a>
      </li>
      <li>
       <a class="text-[#a0b0c0] hover:text-white transition-colors duration-300 tracking-wide font-medium" href="#">
        Mobile App
       </a>
      </li>
      <li>
       <a class="text-[#a0b0c0] hover:text-white transition-colors duration-300 tracking-wide font-medium" href="#">
        Blog
       </a>
      </li>
     </ul>

     <!-- Desktop CTA Button -->
     <button class="hidden lg:inline-flex items-center border-2 border-white/30 rounded-full text-white text-sm font-medium px-6 py-2.5 hover:bg-white hover:text-[#0a1e3a] hover:border-white transition-all duration-300 tracking-wide backdrop-blur-sm" type="button">
      Contact Flexe
      <i class="fas fa-chevron-down ml-2 text-xs"></i>
     </button>

     <!-- Mobile Menu Button -->
     <button id="mobile-menu-button" class="lg:hidden flex items-center justify-center w-10 h-10 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-300" type="button">
      <i class="fas fa-bars text-lg"></i>
     </button>
    </nav>

    <!-- Mobile Navigation Overlay -->
    <div id="mobile-menu" class="fixed inset-0 bg-[#0a1e3a]/95 backdrop-blur-lg z-50 lg:hidden transform translate-x-full transition-transform duration-300 ease-in-out">
     <div class="flex flex-col h-full">
      <!-- Mobile Header -->
      <div class="flex items-center justify-between p-6 border-b border-white/10">
       <div class="flex items-center space-x-3">
        <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-[#4caf50] to-[#2e7d32] flex items-center justify-center">
         <i class="fas fa-warehouse text-white text-sm"></i>
        </div>
        <div class="text-white font-serif text-xl font-bold">Flexe</div>
       </div>
       <button id="mobile-menu-close" class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center text-white hover:bg-white/20 transition-colors duration-300">
        <i class="fas fa-times text-lg"></i>
       </button>
      </div>

      <!-- Mobile Navigation Links -->
      <div class="flex-1 px-6 py-8">
       <ul class="space-y-6">
        <li>
         <a class="flex items-center justify-between text-white text-lg font-semibold py-3 border-b border-white/10 hover:text-[#4caf50] transition-colors duration-300" href="#">
          <span>Home</span>
          <i class="fas fa-chevron-right text-sm"></i>
         </a>
        </li>
        <li>
         <a class="flex items-center justify-between text-[#a0b0c0] text-lg font-medium py-3 border-b border-white/10 hover:text-white transition-colors duration-300" href="#">
          <span>Pricing</span>
          <i class="fas fa-chevron-right text-sm"></i>
         </a>
        </li>
        <li>
         <a class="flex items-center justify-between text-[#a0b0c0] text-lg font-medium py-3 border-b border-white/10 hover:text-white transition-colors duration-300" href="#">
          <span>Mobile App</span>
          <i class="fas fa-chevron-right text-sm"></i>
         </a>
        </li>
        <li>
         <a class="flex items-center justify-between text-[#a0b0c0] text-lg font-medium py-3 border-b border-white/10 hover:text-white transition-colors duration-300" href="#">
          <span>Blog</span>
          <i class="fas fa-chevron-right text-sm"></i>
         </a>
        </li>
       </ul>

       <!-- Mobile CTA Button -->
       <div class="mt-8">
        <button class="w-full bg-[#4caf50] hover:bg-[#3a8e3a] text-white text-lg font-semibold rounded-xl py-4 transition-colors duration-300 shadow-lg" type="button">
         Contact Flexe
        </button>
       </div>
      </div>
     </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-12 gap-10 md:gap-16 items-center">
     <div class="md:col-span-6 max-w-lg">
      <h1 class="text-white font-serif text-6xl md:text-7xl leading-[1.1] mb-6 tracking-tight">
       Warehouse
       <br/>
       management
       <br/>
       software.
      </h1>
      <p class="text-[#a0b0c0] text-sm md:text-base mb-10 max-w-[420px] tracking-wide">
       World's simplest and most efficient web-based multi-channel order fulfillment and warehouse management software (WMS).
      </p>
      <div class="flex space-x-6">
       <button class="bg-[#4caf50] hover:bg-[#3a8e3a] text-white text-sm font-semibold rounded-full px-8 py-3 flex items-center space-x-3 shadow-lg card-shadow transition" type="button">
        <span>
         GET STARTED
        </span>
        <i class="fas fa-chevron-right text-sm">
        </i>
       </button>
       <button class="text-white text-sm font-light flex items-center space-x-3 hover:underline tracking-wide" type="button">
        <span>
         REQUEST DEMO
        </span>
        <i class="fas fa-chevron-right text-sm">
        </i>
       </button>
      </div>
     </div>
     <div class="md:col-span-6 relative flex justify-center md:justify-end">
      <!-- Card 1 -->
      <div class="absolute top-14 left-14 w-40 h-40 rounded-xl bg-gradient-to-b from-white/90 to-white/20 backdrop-blur-[12px] shadow-2xl flex flex-col justify-between p-6 text-[#0a1e3a] font-sans card-shadow" style="font-feature-settings: 'tnum';">
       <div class="flex justify-between items-center text-sm font-semibold tracking-wide">
        <span>
         Products
        </span>
        <span class="text-[#f97316]">
         48%
        </span>
       </div>
       <div class="text-4xl font-extrabold tracking-tight">
        872
       </div>
       <div class="text-sm font-light tracking-wide">
        April
       </div>
      </div>
      <!-- Card 2 -->
      <div class="absolute top-28 left-56 w-40 h-40 rounded-xl bg-gradient-to-b from-white/90 to-white/20 backdrop-blur-[12px] shadow-2xl flex flex-col justify-center items-center text-[#0a1e3a] font-sans card-shadow" style="font-feature-settings: 'tnum';">
       <img alt="Bar chart graph with 872 highlighted dot in orange circle" class="mb-3" height="90" src="https://storage.googleapis.com/a1aa/image/434567ad-6d51-44c6-6d5e-74e895d6d46f.jpg" width="90"/>
       <div class="text-sm font-semibold tracking-wide">
        872
       </div>
      </div>
      <!-- Arrow icon -->
      <svg class="absolute top-40 left-[calc(100%/2+5rem)] w-12 h-12 text-white opacity-80" fill="none" stroke="currentColor" stroke-width="2.5" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
       <path d="M13 7l5 5m0 0l-5 5m5-5H6" stroke-linecap="round" stroke-linejoin="round">
       </path>
      </svg>
      <!-- Circular badge bottom right -->
      <div class="absolute bottom-6 right-6 w-20 h-20 rounded-full border border-white/40 text-white/60 text-[11px] font-light flex items-center justify-center text-center tracking-wide card-shadow" style="font-feature-settings: 'tnum';">
       Warehouse Management Software
      </div>
      <!-- Lock icon top right -->
      <div class="absolute top-8 right-8 w-10 h-10 rounded-full bg-white/25 flex items-center justify-center text-white shadow-lg" title="Secure">
       <i class="fas fa-lock text-sm">
       </i>
      </div>
     </div>
    </div>
   </div>
  </header>
  <!-- Industries Section -->
  <section class="bg-white max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 rounded-b-3xl shadow-lg card-shadow">
   <div class="grid grid-cols-1 md:grid-cols-12 gap-12">
    <div class="md:col-span-7 grid grid-cols-2 gap-10">
     <div>
      <div class="flex items-center space-x-3 mb-2">
       <div class="w-4 h-4 rounded-full bg-[#f97316] shadow-md shadow-[#f97316]/50">
       </div>
       <h3 class="text-sm font-semibold text-[#0a1e3a] tracking-wide">
        Food &amp; Beverage
       </h3>
      </div>
      <p class="text-[10px] text-[#7a7a7a] leading-tight tracking-wide">
       Erat condimentum consectetur dignissim convallis imperdiet nunc risus facilisi.
      </p>
     </div>
     <div>
      <div class="flex items-center space-x-3 mb-2">
       <div class="w-4 h-4 rounded-full bg-[#f97316] shadow-md shadow-[#f97316]/50">
       </div>
       <h3 class="text-sm font-semibold text-[#0a1e3a] tracking-wide">
        Home Improvement
       </h3>
      </div>
      <p class="text-[10px] text-[#7a7a7a] leading-tight tracking-wide">
       Erat condimentum consectetur dignissim convallis imperdiet nunc risus facilisi.
      </p>
     </div>
     <div>
      <div class="flex items-center space-x-3 mb-2">
       <div class="w-4 h-4 rounded-full bg-[#f97316] shadow-md shadow-[#f97316]/50">
       </div>
       <h3 class="text-sm font-semibold text-[#0a1e3a] tracking-wide">
        Pharmaceutical
       </h3>
      </div>
      <p class="text-[10px] text-[#7a7a7a] leading-tight tracking-wide">
       Erat condimentum consectetur dignissim convallis imperdiet nunc risus facilisi.
      </p>
     </div>
     <div>
      <div class="flex items-center space-x-3 mb-2">
       <div class="w-4 h-4 rounded-full bg-[#f97316] shadow-md shadow-[#f97316]/50">
       </div>
       <h3 class="text-sm font-semibold text-[#0a1e3a] tracking-wide">
        Internet Retailers
       </h3>
      </div>
      <p class="text-[10px] text-[#7a7a7a] leading-tight tracking-wide">
       Erat condimentum consectetur dignissim convallis imperdiet nunc risus facilisi.
      </p>
     </div>
    </div>
    <div class="md:col-span-5 flex flex-col justify-center">
     <h2 class="text-3xl font-serif font-normal mb-3 tracking-tight">
      Industries We Services
     </h2>
     <p class="text-sm text-[#7a7a7a] mb-8 leading-relaxed tracking-wide">
      Paperless WMS (Warehouse Management System) is trusted by leading businesses across the globe with features suited.
     </p>
     <button class="bg-[#4caf50] hover:bg-[#3a8e3a] text-white text-sm font-semibold rounded-full px-8 py-3 w-max shadow-lg card-shadow transition" type="button">
      VIEW MORE
     </button>
    </div>
   </div>
  </section>
  <!-- Inventory Flow Section -->
  <section class="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 grid grid-cols-1 md:grid-cols-12 gap-16 items-center">
   <div class="md:col-span-6 max-w-lg">
    <h2 class="text-4xl font-serif font-normal mb-8 leading-tight tracking-tight">
     Inventory Flow Championed
    </h2>
    <ul class="text-sm text-[#7a7a7a] space-y-4 mb-10 list-disc list-inside marker:text-[#f97316] tracking-wide">
     <li>
      Store products safely with specialty storage options like cold storage, hazardous material, high value inventory etc.
     </li>
     <li>
      Periodic stock take and automated replenishments enable you to monitor inventory levels and eliminate stock out events.
     </li>
     <li>
      Whether you track your inventory by batch, lot or even serial number, always get 100% accurate audits.
     </li>
    </ul>
    <div class="flex space-x-6">
     <button class="bg-[#4caf50] hover:bg-[#3a8e3a] text-white text-sm font-semibold rounded-full px-8 py-3 flex items-center space-x-3 shadow-lg card-shadow transition" type="button">
      <span>
       SET IT ACTION
      </span>
     </button>
     <button class="text-sm font-semibold underline text-[#0a1e3a] tracking-wide" type="button">
      TRY IT FREE
     </button>
    </div>
   </div>
   <div class="md:col-span-6 relative bg-gradient-to-r from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] rounded-xl p-8 text-white max-w-md mx-auto md:mx-0 card-shadow">
    <div class="mb-6">
     <h3 class="text-lg font-semibold mb-3 tracking-wide">
      Overview
     </h3>
     <div class="flex justify-between text-sm font-light mb-2 tracking-wide">
      <span class="text-[#4caf50]">
       + 12%
      </span>
      <span class="text-[#4caf50]">
       + 4.8%
      </span>
     </div>
     <div class="flex justify-between font-extrabold text-4xl mb-2 tracking-tight" style="font-feature-settings: 'tnum';">
      <span>
       $450m
      </span>
      <span>
       8.901
      </span>
     </div>
     <div class="flex justify-between text-sm font-light tracking-wide">
      <span>
       Total Income
      </span>
      <span>
       Total Sales
      </span>
     </div>
    </div>
    <div class="bg-[#0a1e3a] rounded-lg flex items-center justify-between px-6 py-3 text-sm font-light cursor-pointer select-none shadow-lg">
     <div class="flex items-center space-x-3">
      <i class="fas fa-comment-alt text-white/80 text-base">
      </i>
      <span>
       2022
      </span>
      <span>
       Report Overview
      </span>
     </div>
     <button aria-label="Next" class="bg-[#f97316] hover:bg-[#d05a00] rounded-lg w-10 h-10 flex items-center justify-center text-white shadow-md transition">
      <i class="fas fa-arrow-right">
      </i>
     </button>
    </div>
   </div>
  </section>
  <!-- Logos Section -->
  <section class="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-16 flex justify-center space-x-16">
   <img alt="FedEx Logistics company logo in blue and orange" class="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/9a5510d9-5102-4789-77bf-391b5a7a6a8c.jpg" width="144"/>
   <img alt="Crane Worldwide Logistics company logo in green and blue" class="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/0f431638-d78a-4551-cd41-7644bfec8fae.jpg" width="144"/>
   <img alt="Bollore Logistics company logo with red and blue text and arrow" class="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/4819675f-e061-4a06-2144-05b136d669e9.jpg" width="144"/>
   <img alt="CEVA Logistics company logo with red and black text" class="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/c734b295-1592-409b-e4a1-f0fbf795847a.jpg" width="144"/>
   <img alt="HAVI company logo with green and blue bars and text" class="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/bf5a67af-3d7f-42dc-2374-c55cdec36f40.jpg" width="144"/>
  </section>
  <!-- E-commerce Solution Section -->
  <section class="relative bg-gradient-to-r from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-24 flex flex-col md:flex-row items-center md:items-start gap-16 md:gap-24 rounded-t-3xl shadow-lg card-shadow">
   <div class="absolute -top-8 left-1/2 -translate-x-1/2 w-14 h-14 rounded-full bg-[#f97316] flex items-center justify-center text-white text-lg font-light cursor-default select-none z-10 shadow-lg">
    <i class="fas fa-arrow-down">
    </i>
   </div>
   <div class="relative max-w-xs md:max-w-[300px] shadow-2xl rounded-3xl overflow-hidden">
    <div class="absolute top-5 left-5 w-10 h-10 rounded-full bg-white/25 flex items-center justify-center text-white text-lg cursor-default select-none shadow-md" title="Clock icon">
     <i class="fas fa-clock">
     </i>
    </div>
    <img alt="Mobile phone screen showing inventory app dashboard with charts and stats" class="rounded-3xl" height="560" src="https://storage.googleapis.com/a1aa/image/1347993e-5ce4-4703-cd22-1a5752fe0d69.jpg" width="280"/>
    <div class="absolute bottom-14 right-14 w-20 h-20 rounded-full border border-white/40 text-white/60 text-[11px] font-light flex items-center justify-center text-center cursor-default select-none tracking-wide card-shadow" style="font-feature-settings: 'tnum';">
     Warehouse Management Software
    </div>
    <div class="absolute bottom-28 right-20 w-10 h-10 rounded-full bg-white/25 flex items-center justify-center text-white text-lg cursor-default select-none shadow-md" title="Arrow down icon">
     <i class="fas fa-arrow-down">
     </i>
    </div>
   </div>
   <div class="max-w-lg text-white text-base leading-relaxed tracking-wide">
    <h3 class="text-2xl font-serif font-normal mb-8 tracking-tight">
     We have a solution for eCommerce challenges of all shapes &amp; sizes across Asia Pacific
    </h3>
    <p class="mb-10 text-sm leading-relaxed tracking-wide">
     Whether you are an eCommerce distributor, an asset-light enabler, an offline retailer transforming to omni-channel, a B2B logistics company trying to start eCommerce fulfillment, or an SME scaling online business, you have your own unique set of challenges.
    </p>
    <p class="mb-10 text-sm leading-relaxed tracking-wide">
     Anchanto’s cutting-edge technology simplifies backend operations, and helps you achieve unprecedented productivity through automated selling management.
    </p>
    <div class="flex space-x-6">
     <button class="bg-[#4caf50] hover:bg-[#3a8e3a] text-white text-sm font-semibold rounded-full px-8 py-3 flex items-center space-x-3 shadow-lg card-shadow transition" type="button">
      <span>
       SET IT ACTION
      </span>
     </button>
     <button class="text-sm font-semibold underline text-white tracking-wide" type="button">
      TRY IT FREE
     </button>
    </div>
   </div>
  </section>
  <!-- Discover Section -->
  <section class="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-24 text-center">
   <h2 class="text-3xl font-serif font-normal mb-24 tracking-tight">
    Discover Who We Are
   </h2>
   <div class="flex flex-col md:flex-row justify-center gap-16 max-w-5xl mx-auto mb-24">
    <div class="max-w-xs">
     <div class="mx-auto mb-6 w-14 h-14 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl cursor-default select-none shadow-lg">
      <i class="fas fa-cogs">
      </i>
     </div>
     <h4 class="text-sm font-semibold mb-3 tracking-wide">
      Features
     </h4>
     <p class="text-[11px] text-[#7a7a7a] leading-tight tracking-wide">
      Ensure maximum output from your warehouse to achieve rising success on both B2B and B2C fronts with Wareo’s smart &amp; intuitive features.
     </p>
    </div>
    <div class="max-w-xs">
     <div class="mx-auto mb-6 w-14 h-14 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl cursor-default select-none shadow-lg">
      <i class="fas fa-industry">
      </i>
     </div>
     <h4 class="text-sm font-semibold mb-3 tracking-wide">
      Industry Solutions
     </h4>
     <p class="text-[11px] text-[#7a7a7a] leading-tight tracking-wide">
      Discover how you can start eCommerce fulfillment, streamline warehouse operations, manage logistics in-house, or solve your logistics challenges.
     </p>
    </div>
    <div class="max-w-xs">
     <div class="mx-auto mb-6 w-14 h-14 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl cursor-default select-none shadow-lg">
      <i class="fas fa-file-alt">
      </i>
     </div>
     <h4 class="text-sm font-semibold mb-3 tracking-wide">
      Case Studies
     </h4>
     <p class="text-[11px] text-[#7a7a7a] leading-tight tracking-wide">
      Find out how multiple global players such as Nestle, Luxasia, Zilingo &amp; more have defined their transformational journeys with Wareo.
     </p>
    </div>
   </div>
   <hr class="border-t border-[#e3e8ef] mb-16"/>
   <div class="max-w-xl mx-auto">
    <div class="w-20 h-20 rounded-full bg-[#0a1e3a] mx-auto mb-8 flex items-center justify-center text-[#f97316] text-4xl cursor-default select-none shadow-lg">
     <i class="fas fa-quote-left">
     </i>
    </div>
    <p class="text-lg text-[#0a1e3a] mb-6 leading-relaxed tracking-wide">
     We found the Inventory app to be a powerful tool to better organize our warehouse. It's simple and easily customizable.
    </p>
    <p class="text-sm text-[#f97316] font-semibold mb-12 tracking-wide">
     Mario Riva, COO
    </p>
    <div class="flex justify-center space-x-16 text-[#0a1e3a] text-sm font-light">
     <button aria-label="Previous testimonial" class="rounded-full border border-[#0a1e3a] w-10 h-10 flex items-center justify-center hover:bg-[#0a1e3a] hover:text-white transition shadow-md" type="button">
      <i class="fas fa-arrow-left">
      </i>
     </button>
     <button aria-label="Next testimonial" class="rounded-full border border-[#0a1e3a] w-10 h-10 flex items-center justify-center hover:bg-[#0a1e3a] hover:text-white transition shadow-md" type="button">
      <i class="fas fa-arrow-right">
      </i>
     </button>
    </div>
   </div>
  </section>
  <!-- Footer -->
  <footer class="bg-[#0a1e3a] text-white text-sm font-light">
   <div class="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 grid grid-cols-1 md:grid-cols-12 gap-16">
    <div class="md:col-span-3 space-y-6">
     <h3 class="font-serif font-semibold text-lg select-none tracking-wide">
      Flexe
     </h3>
     <p class="text-[11px] leading-tight max-w-[260px] tracking-wide">
      World's simplest and most efficient web-based multi-channel order fulfillment and warehouse management software (WMS).
     </p>
     <div class="flex space-x-6 text-[#4caf50] text-xl">
      <a aria-label="Facebook" class="hover:text-[#3a8e3a] transition" href="#">
       <i class="fab fa-facebook-f">
       </i>
      </a>
      <a aria-label="YouTube" class="hover:text-[#3a8e3a] transition" href="#">
       <i class="fab fa-youtube">
       </i>
      </a>
      <a aria-label="GitHub" class="hover:text-[#3a8e3a] transition" href="#">
       <i class="fab fa-github">
       </i>
      </a>
     </div>
    </div>
    <div class="md:col-span-2">
     <h4 class="font-semibold mb-6 tracking-wide">
      Product
     </h4>
     <ul class="space-y-3 tracking-wide">
      <li>
       <a class="hover:underline" href="#">
        Flexe
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Euforia
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Pricing
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Integration
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        API
       </a>
      </li>
     </ul>
    </div>
    <div class="md:col-span-3">
     <h4 class="font-semibold mb-6 tracking-wide">
      Solutions
     </h4>
     <ul class="space-y-3 tracking-wide">
      <li>
       <a class="hover:underline" href="#">
        eCommerce Distributors
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Omnichannel Retailers
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        3PL Transformation
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Food eCommerce
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Cross-Border eCommerce
       </a>
      </li>
     </ul>
    </div>
    <div class="md:col-span-4">
     <h4 class="font-semibold mb-6 tracking-wide">
      Company
     </h4>
     <ul class="space-y-3 tracking-wide">
      <li>
       <a class="hover:underline" href="#">
        About Flexe
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Leadership
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Journey
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Join with Flexe
       </a>
      </li>
      <li>
       <a class="hover:underline" href="#">
        Resources
       </a>
      </li>
     </ul>
    </div>
   </div>
   <div class="border-t border-white/20 text-center py-6 text-[11px] font-light select-none tracking-wide">
    2022, Created with love
    <span class="text-[#f97316]">
     ❤️
    </span>
   </div>
  </footer>

  <!-- Mobile Menu JavaScript -->
  <script>
   document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuClose = document.getElementById('mobile-menu-close');

    // Open mobile menu
    mobileMenuButton.addEventListener('click', function() {
     mobileMenu.classList.remove('translate-x-full');
     mobileMenu.classList.add('translate-x-0');
     document.body.style.overflow = 'hidden';
    });

    // Close mobile menu
    function closeMobileMenu() {
     mobileMenu.classList.remove('translate-x-0');
     mobileMenu.classList.add('translate-x-full');
     document.body.style.overflow = '';
    }

    mobileMenuClose.addEventListener('click', closeMobileMenu);

    // Close menu when clicking outside
    mobileMenu.addEventListener('click', function(e) {
     if (e.target === mobileMenu) {
      closeMobileMenu();
     }
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
     if (e.key === 'Escape' && !mobileMenu.classList.contains('translate-x-full')) {
      closeMobileMenu();
     }
    });
   });
  </script>
 </body>
</html>