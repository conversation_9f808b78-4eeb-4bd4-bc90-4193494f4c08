'use client';

import { useState } from 'react';
import Image from "next/image";
import Header from '../components/Header';
import Footer from '../components/Footer';

export default function AboutPage() {
  const [selectedLeader, setSelectedLeader] = useState(null);

  const leaders = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Co-founder & CEO",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "<PERSON> is a seasoned technology executive, with leadership experience in both startups and large, global corporations. Prior to co-founding Flexe, <PERSON> was CEO of AdReady, a Seattle-based advertising technology company."
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      title: "Chief Financial Officer",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "<PERSON><PERSON> is the Chief Financial Officer at Flexe. Prior to joining Flexe, he served as CFO for Icertis and Chef Software, as well as various finance, operations and marketing leadership roles at Microsoft and Amazon."
    },
    {
      id: 3,
      name: "Pirasenna Thiyagarajan",
      title: "Chief Product and Technology Officer",
      image: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Pirasenna is the Chief Product and Technology Officer at Flexe. Prior to joining Flexe, he served as the Chief Technology Officer for ServUs, as well as various technology, digital, and engineering roles."
    },
    {
      id: 4,
      name: "Katie Carter",
      title: "VP, Sales",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Katie is the VP of Sales at Flexe. Prior to joining Flexe, she served in business development roles at Crowley Maritime. She holds a Communications degree from Florida State University."
    },
    {
      id: 5,
      name: "Ben Cooke",
      title: "VP, Revenue Operations",
      image: "https://images.unsplash.com/photo-**********-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Ben Cooke is the VP of Revenue Operations at Flexe. Prior to joining Flexe, he served various revenue operations roles at Florence Healthcare, Bolt, and Convoy Inc."
    },
    {
      id: 6,
      name: "Holly Ann Walker",
      title: "VP, People",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Holly is VP of People at Flexe. Prior to joining Flexe, she held human capital roles at Smartsheet, Zulily and Geocaching. She holds a bachelor's degree in Arabic Language and Middle Eastern Studies."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="about" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Modern shipping containers and logistics"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-building text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">About Flexe</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight mb-8">
              To create the open logistics network that optimizes the
              <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
                global delivery of goods.
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Flexe provides flexible warehousing infrastructure that enables enterprises to evolve,
              optimize and prepare networks for long-term strategic growth.
            </p>
          </div>
        </div>
      </section>

      {/* About Us Section */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              {/* Section Badge */}
              <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
                <i className="fas fa-info-circle text-[#E6B24B] mr-3"></i>
                <span className="text-white font-semibold">Our Story</span>
              </div>

              {/* Main Content */}
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 leading-tight">
                Flexible Warehousing
                <span className="block text-[#E6B24B]">Infrastructure</span>
              </h2>

              <p className="text-xl text-gray-300 leading-relaxed mb-8">
                Tech-enabled warehouse services that allow enterprises to evolve, optimize and prepare networks for long-term strategic growth.
              </p>

              <p className="text-lg text-gray-300 leading-relaxed mb-8">
                A single technology integration opens access to <span className="text-[#4caf50] font-semibold">700+ warehouse operators</span> across the U.S. and Canada. Founded in 2013 and headquartered in Seattle, Flexe brings deep logistics expertise and enterprise-grade technology.
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <div className="text-3xl font-bold text-[#E6B24B] mb-2">700+</div>
                  <div className="text-gray-300 text-sm">Warehouse Partners</div>
                </div>
                <div className="text-center p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <div className="text-3xl font-bold text-[#4caf50] mb-2">2013</div>
                  <div className="text-gray-300 text-sm">Founded</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative overflow-hidden rounded-2xl">
                <img
                  alt="Modern logistics and shipping operations"
                  className="w-full h-96 object-cover"
                  src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Section Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-heart text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Our Values</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              What Drives Us
              <span className="block text-[#4caf50]">Forward</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our core values guide every decision we make and every relationship we build.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Value 1 */}
            <div className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-bullseye text-black text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-[#E6B24B] transition-colors duration-300">
                Deliver Customer Impact
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Customers' success is our success. We build strong relationships with both our internal and external customers, deeply understand their needs, and deliver solutions that drive valuable impact.
              </p>
            </div>

            {/* Value 2 */}
            <div className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#4caf50] to-[#2e7d32] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-brain text-black text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-[#4caf50] transition-colors duration-300">
                Think Critically
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Disrupting the Logistics industry is a puzzle full of complexities and challenges. We solve the right problems by demonstrating curiosity, being data-driven, and understanding root causes.
              </p>
            </div>

            {/* Value 3 */}
            <div className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-users text-black text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-[#E6B24B] transition-colors duration-300">
                Work Together
              </h3>
              <p className="text-gray-300 leading-relaxed">
                We make each other better, regardless of tenure or title. We value diversity of thought and experience, trust others, and show respect through direct feedback and collaboration.
              </p>
            </div>

            {/* Value 4 */}
            <div className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#4caf50] to-[#2e7d32] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-shield-alt text-black text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-[#4caf50] transition-colors duration-300">
                Be Accountable
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Trust is earned or lost in every action. We are not perfect, but we take ownership of our decisions and actions, holding ourselves and others accountable to deliver on our commitments with transparency and integrity.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team Section */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Section Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-users text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Leadership Team</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Meet Our
              <span className="block text-[#E6B24B]">Leadership Team</span>
            </h2>

            {/* Team Image */}
            <div className="relative overflow-hidden rounded-2xl mb-12">
              <img
                alt="Team collaboration in modern office"
                className="w-full h-64 object-cover"
                src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            {leaders.map((leader) => (
              <div
                key={leader.id}
                className="text-center cursor-pointer group"
                onClick={() => setSelectedLeader(leader)}
              >
                <div className="relative mb-4 overflow-hidden rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10">
                  <img
                    alt={leader.name}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    src={leader.image}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-white text-sm font-semibold">Click to learn more</div>
                  </div>
                </div>
                <h3 className="text-white font-semibold mb-1 group-hover:text-[#E6B24B] transition-colors duration-300">{leader.name}</h3>
                <p className="text-gray-400 text-sm">{leader.title}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Team CTA */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Interested in joining the
              <span className="block text-[#4caf50]">Flexe team?</span>
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Flexe is disrupting the $1.6 trillion logistics & supply chain industry. Join our fast-growing team of people dedicated to making a difference.
            </p>
            <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] hover:from-[#D4A843] hover:to-[#E6B24B] text-black font-bold px-10 py-5 rounded-xl text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
              View Open Positions
            </button>
          </div>
        </div>
      </section>

      {/* Leader Modal */}
      {selectedLeader && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4" onClick={() => setSelectedLeader(null)}>
          <div className="bg-gradient-to-br from-[#0a1e3a] to-[#2a5a3a] rounded-2xl p-8 max-w-2xl w-full border border-white/20 backdrop-blur-sm" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-start space-x-6 mb-6">
              <img
                alt={selectedLeader.name}
                className="w-24 h-24 object-cover rounded-xl"
                src={selectedLeader.image}
              />
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-white mb-2">{selectedLeader.name}</h3>
                <p className="text-[#E6B24B] font-semibold">{selectedLeader.title}</p>
              </div>
              <button
                onClick={() => setSelectedLeader(null)}
                className="text-gray-400 hover:text-white transition-colors duration-300 p-2"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            </div>
            <p className="text-gray-300 leading-relaxed">{selectedLeader.bio}</p>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}
