export default function Footer() {
  return (
    <footer className="relative overflow-hidden">
      {/* Glassy Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]" />

      {/* Glass Effect Overlay */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-md" />

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 py-16 text-white">

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-10 mb-14">

          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 rounded-lg bg-white flex items-center justify-center shadow-md">
                <i className="fas fa-network-wired text-black text-xl"></i>
              </div>
              <div>
                <h3 className="text-2xl font-bold">Flexe</h3>
                <p className="text-gray-300 text-sm">Warehouse Management</p>
              </div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed text-sm">
              Transform your supply chain with our warehouse management platform. Trusted by Fortune 500 companies worldwide.
            </p>

            {/* Social Links */}
            <div className="flex space-x-3">
              {["linkedin", "twitter", "facebook", "instagram"].map((icon, i) => (
                <a
                  key={i}
                  href="#"
                  className="w-10 h-10 bg-white/10 border border-white/20 rounded-lg flex items-center justify-center text-white hover:bg-[#E6B24B] hover:text-black transition-all duration-300"
                >
                  <i className={`fab fa-${icon}`}></i>
                </a>
              ))}
            </div>
          </div>

          {/* Solutions */}
          <div>
            <h4 className="font-semibold text-lg mb-6">Solutions</h4>
            <ul className="space-y-3 text-sm">
              {["Warehousing", "Distribution", "Fulfillment", "Inventory Management"].map((item, idx) => (
                <li key={idx}>
                  <a href="#" className="text-gray-300 hover:text-[#E6B24B] transition duration-300">{item}</a>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="font-semibold text-lg mb-6">Company</h4>
            <ul className="space-y-3 text-sm">
              {["About Us", "Careers", "Newsroom", "Contact"].map((item, idx) => (
                <li key={idx}>
                  <a href="#" className="text-gray-300 hover:text-[#E6B24B] transition duration-300">{item}</a>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h4 className="font-semibold text-lg mb-6">Resources</h4>
            <ul className="space-y-3 text-sm">
              {["Articles", "Case Studies", "Webinars", "How It Works"].map((item, idx) => (
                <li key={idx}>
                  <a href="#" className="text-gray-300 hover:text-[#E6B24B] transition duration-300">{item}</a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/20 pt-8 flex flex-col md:flex-row justify-between items-center gap-4 text-sm text-gray-400">
          <div className="flex items-center space-x-4">
            <p>© {new Date().getFullYear()} Flexe. All rights reserved.</p>
            <div className="flex items-center space-x-2">
              <i className="fas fa-shield-alt text-[#4caf50]"></i>
              <span>SOC 2 Compliant</span>
            </div>
          </div>
          <div className="flex space-x-5">
            {["Privacy Policy", "Terms of Service", "Cookie Policy"].map((item, idx) => (
              <a
                key={idx}
                href="#"
                className="hover:text-[#E6B24B] transition duration-300"
              >
                {item}
              </a>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
}
