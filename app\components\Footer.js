export default function Footer() {
  return (
    <footer className="relative overflow-hidden">
      {/* Modern Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]"></div>

      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(230, 178, 75, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
            linear-gradient(45deg, rgba(255, 255, 255, 0.02) 25%, transparent 25%),
            linear-gradient(-45deg, rgba(255, 255, 255, 0.02) 25%, transparent 25%)
          `,
          backgroundSize: '60px 60px, 80px 80px, 20px 20px, 20px 20px',
          animation: 'float 20s ease-in-out infinite'
        }}></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-2 h-2 bg-[#E6B24B] rounded-full opacity-60 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-[#4caf50] rounded-full opacity-40 animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-[#E6B24B] rounded-full opacity-50 animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-20 right-1/3 w-1 h-1 bg-[#4caf50] rounded-full opacity-30 animate-pulse" style={{animationDelay: '3s'}}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 py-20">

        {/* Enhanced Header Section */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center space-x-4 mb-6">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-[#E6B24B] to-[#D4A843] flex items-center justify-center shadow-2xl transform hover:scale-105 transition-all duration-300">
              <i className="fas fa-network-wired text-black text-2xl"></i>
            </div>
            <div>
              <h2 className="text-4xl font-bold text-white mb-2">Flexe</h2>
              <p className="text-[#E6B24B] font-semibold text-lg">Warehouse Management Solutions</p>
            </div>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Transform your supply chain with North America's largest flexible warehousing network.
            <span className="text-[#4caf50] font-semibold"> Trusted by Fortune 500 companies</span> worldwide for scalable, efficient logistics solutions.
          </p>
        </div>

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">

          {/* Solutions */}
          <div className="group">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#E6B24B]/20 to-[#D4A843]/20 flex items-center justify-center">
                <i className="fas fa-cogs text-[#E6B24B] text-sm"></i>
              </div>
              <h4 className="font-bold text-white text-lg">Solutions</h4>
            </div>
            <ul className="space-y-3">
              <li>
                <a href="/solutions" className="flex items-center space-x-2 text-gray-300 hover:text-[#E6B24B] transition-all duration-300 group/link">
                  <i className="fas fa-warehouse text-xs text-gray-500 group-hover/link:text-[#E6B24B]"></i>
                  <span>Warehousing</span>
                </a>
              </li>
              <li>
                <a href="/solutions/distribution" className="flex items-center space-x-2 text-gray-300 hover:text-[#E6B24B] transition-all duration-300 group/link">
                  <i className="fas fa-truck text-xs text-gray-500 group-hover/link:text-[#E6B24B]"></i>
                  <span>Distribution</span>
                </a>
              </li>
              <li>
                <a href="/solutions/fulfillment" className="flex items-center space-x-2 text-gray-300 hover:text-[#E6B24B] transition-all duration-300 group/link">
                  <i className="fas fa-box text-xs text-gray-500 group-hover/link:text-[#E6B24B]"></i>
                  <span>Fulfillment</span>
                </a>
              </li>
              <li>
                <a href="/solutions/capacity" className="flex items-center space-x-2 text-gray-300 hover:text-[#E6B24B] transition-all duration-300 group/link">
                  <i className="fas fa-chart-line text-xs text-gray-500 group-hover/link:text-[#E6B24B]"></i>
                  <span>Inventory Management</span>
                </a>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div className="group">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#4caf50]/20 to-[#2e7d32]/20 flex items-center justify-center">
                <i className="fas fa-building text-[#4caf50] text-sm"></i>
              </div>
              <h4 className="font-bold text-white text-lg">Company</h4>
            </div>
            <ul className="space-y-3">
              <li>
                <a href="/about" className="flex items-center space-x-2 text-gray-300 hover:text-[#4caf50] transition-all duration-300 group/link">
                  <i className="fas fa-info-circle text-xs text-gray-500 group-hover/link:text-[#4caf50]"></i>
                  <span>About Us</span>
                </a>
              </li>
              <li>
                <a href="/careers" className="flex items-center space-x-2 text-gray-300 hover:text-[#4caf50] transition-all duration-300 group/link">
                  <i className="fas fa-users text-xs text-gray-500 group-hover/link:text-[#4caf50]"></i>
                  <span>Careers</span>
                </a>
              </li>
              <li>
                <a href="/newsroom" className="flex items-center space-x-2 text-gray-300 hover:text-[#4caf50] transition-all duration-300 group/link">
                  <i className="fas fa-newspaper text-xs text-gray-500 group-hover/link:text-[#4caf50]"></i>
                  <span>Newsroom</span>
                </a>
              </li>
              <li>
                <a href="/contact" className="flex items-center space-x-2 text-gray-300 hover:text-[#4caf50] transition-all duration-300 group/link">
                  <i className="fas fa-envelope text-xs text-gray-500 group-hover/link:text-[#4caf50]"></i>
                  <span>Contact</span>
                </a>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div className="group">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#E6B24B]/20 to-[#D4A843]/20 flex items-center justify-center">
                <i className="fas fa-book text-[#E6B24B] text-sm"></i>
              </div>
              <h4 className="font-bold text-white text-lg">Resources</h4>
            </div>
            <ul className="space-y-3">
              <li>
                <a href="/resources" className="flex items-center space-x-2 text-gray-300 hover:text-[#E6B24B] transition-all duration-300 group/link">
                  <i className="fas fa-file-alt text-xs text-gray-500 group-hover/link:text-[#E6B24B]"></i>
                  <span>Articles</span>
                </a>
              </li>
              <li>
                <a href="/resources" className="flex items-center space-x-2 text-gray-300 hover:text-[#E6B24B] transition-all duration-300 group/link">
                  <i className="fas fa-chart-bar text-xs text-gray-500 group-hover/link:text-[#E6B24B]"></i>
                  <span>Case Studies</span>
                </a>
              </li>
              <li>
                <a href="/resources" className="flex items-center space-x-2 text-gray-300 hover:text-[#E6B24B] transition-all duration-300 group/link">
                  <i className="fas fa-video text-xs text-gray-500 group-hover/link:text-[#E6B24B]"></i>
                  <span>Webinars</span>
                </a>
              </li>
              <li>
                <a href="/how-it-works" className="flex items-center space-x-2 text-gray-300 hover:text-[#E6B24B] transition-all duration-300 group/link">
                  <i className="fas fa-question-circle text-xs text-gray-500 group-hover/link:text-[#E6B24B]"></i>
                  <span>How It Works</span>
                </a>
              </li>
            </ul>
          </div>

          {/* Contact & Social */}
          <div className="group">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#4caf50]/20 to-[#2e7d32]/20 flex items-center justify-center">
                <i className="fas fa-share-alt text-[#4caf50] text-sm"></i>
              </div>
              <h4 className="font-bold text-white text-lg">Connect</h4>
            </div>

            {/* Contact Info */}
            <div className="mb-6 space-y-3">
              <div className="flex items-center space-x-3 text-gray-300">
                <i className="fas fa-map-marker-alt text-[#E6B24B] text-sm w-4"></i>
                <span className="text-sm">Seattle, WA</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <i className="fas fa-phone text-[#E6B24B] text-sm w-4"></i>
                <span className="text-sm">************</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <i className="fas fa-envelope text-[#E6B24B] text-sm w-4"></i>
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>

            {/* Enhanced Social Links */}
            <div className="flex space-x-3">
              <a href="#" className="w-10 h-10 rounded-xl bg-gradient-to-br from-[#0077b5]/20 to-[#0077b5]/10 border border-[#0077b5]/30 flex items-center justify-center hover:from-[#0077b5] hover:to-[#005885] transition-all duration-300 group/social">
                <i className="fab fa-linkedin text-[#0077b5] group-hover/social:text-white"></i>
              </a>
              <a href="#" className="w-10 h-10 rounded-xl bg-gradient-to-br from-[#1da1f2]/20 to-[#1da1f2]/10 border border-[#1da1f2]/30 flex items-center justify-center hover:from-[#1da1f2] hover:to-[#0d8bd9] transition-all duration-300 group/social">
                <i className="fab fa-twitter text-[#1da1f2] group-hover/social:text-white"></i>
              </a>
              <a href="#" className="w-10 h-10 rounded-xl bg-gradient-to-br from-[#4267b2]/20 to-[#4267b2]/10 border border-[#4267b2]/30 flex items-center justify-center hover:from-[#4267b2] hover:to-[#365899] transition-all duration-300 group/social">
                <i className="fab fa-facebook text-[#4267b2] group-hover/social:text-white"></i>
              </a>
              <a href="#" className="w-10 h-10 rounded-xl bg-gradient-to-br from-[#e4405f]/20 to-[#e4405f]/10 border border-[#e4405f]/30 flex items-center justify-center hover:from-[#e4405f] hover:to-[#d62976] transition-all duration-300 group/social">
                <i className="fab fa-instagram text-[#e4405f] group-hover/social:text-white"></i>
              </a>
            </div>
          </div>

        </div>

        {/* Enhanced Newsletter Section */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-[#E6B24B]/10 via-[#4caf50]/10 to-[#E6B24B]/10 rounded-2xl blur-xl"></div>
          <div className="relative bg-gradient-to-r from-[#0a1e3a]/50 via-[#0a2e2a]/50 to-[#2a5a3a]/50 backdrop-blur-sm border border-white/10 rounded-2xl p-8 mb-12">
            <div className="max-w-2xl mx-auto text-center">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[#E6B24B] to-[#D4A843] flex items-center justify-center">
                  <i className="fas fa-envelope text-black text-lg"></i>
                </div>
                <h4 className="text-2xl font-bold text-white">Stay Ahead of the Curve</h4>
              </div>
              <p className="text-gray-300 mb-8 text-lg leading-relaxed">
                Get exclusive insights on warehouse management, supply chain optimization, and industry trends delivered to your inbox.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="flex-1 px-6 py-4 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#E6B24B] focus:border-transparent transition-all duration-300"
                />
                <button className="px-8 py-4 bg-gradient-to-r from-[#E6B24B] to-[#D4A843] hover:from-[#D4A843] hover:to-[#E6B24B] text-black font-bold rounded-xl transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                  Subscribe
                </button>
              </div>
              <p className="text-sm text-gray-400 mt-4">
                Join 10,000+ supply chain professionals. Unsubscribe anytime.
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Bottom Bar */}
        <div className="border-t border-white/10 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">

            {/* Left Side - Copyright & Certifications */}
            <div className="flex flex-col sm:flex-row items-center gap-6">
              <p className="text-gray-400 text-sm">
                © 2024 Flexe, Inc. All rights reserved.
              </p>
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <div className="w-6 h-6 rounded-full bg-[#4caf50]/20 flex items-center justify-center">
                    <i className="fas fa-shield-alt text-[#4caf50] text-xs"></i>
                  </div>
                  <span>SOC 2 Compliant</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <div className="w-6 h-6 rounded-full bg-[#E6B24B]/20 flex items-center justify-center">
                    <i className="fas fa-lock text-[#E6B24B] text-xs"></i>
                  </div>
                  <span>GDPR Ready</span>
                </div>
              </div>
            </div>

            {/* Right Side - Legal Links */}
            <div className="flex flex-wrap items-center gap-6">
              <a href="#" className="text-sm text-gray-400 hover:text-[#E6B24B] transition-colors duration-300">
                Privacy Policy
              </a>
              <a href="#" className="text-sm text-gray-400 hover:text-[#E6B24B] transition-colors duration-300">
                Terms of Service
              </a>
              <a href="#" className="text-sm text-gray-400 hover:text-[#E6B24B] transition-colors duration-300">
                Cookie Policy
              </a>
              <a href="#" className="text-sm text-gray-400 hover:text-[#E6B24B] transition-colors duration-300">
                Accessibility
              </a>
            </div>
          </div>

          {/* Bottom Credits */}
          <div className="mt-6 pt-6 border-t border-white/5 text-center">
            <p className="text-xs text-gray-500">
              Powering the future of flexible warehousing across North America
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
