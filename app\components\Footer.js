export default function Footer() {
  return (
    <footer className="bg-[#0a1e3a] text-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8 py-16">

        {/* Company Info */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-12">
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-[#E6B24B] to-[#D4A843] flex items-center justify-center">
                <i className="fas fa-network-wired text-black text-xl"></i>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">Flexe</h3>
                <p className="text-gray-300 text-sm">Warehouse Management</p>
              </div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Transform your supply chain with our comprehensive warehouse management platform.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-300 hover:text-white hover:bg-[#E6B24B] transition-all duration-300">
                <i className="fab fa-linkedin"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-300 hover:text-white hover:bg-[#E6B24B] transition-all duration-300">
                <i className="fab fa-twitter"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-300 hover:text-white hover:bg-[#E6B24B] transition-all duration-300">
                <i className="fab fa-facebook"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-300 hover:text-white hover:bg-[#E6B24B] transition-all duration-300">
                <i className="fab fa-instagram"></i>
              </a>
            </div>
          </div>

          {/* Solutions */}
          <div>
            <h4 className="font-semibold text-white text-lg mb-6">Solutions</h4>
            <ul className="space-y-4">
              <li>
                <a href="/solutions" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Warehousing
                </a>
              </li>
              <li>
                <a href="/solutions/distribution" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Distribution
                </a>
              </li>
              <li>
                <a href="/solutions/fulfillment" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Fulfillment
                </a>
              </li>
              <li>
                <a href="/solutions/capacity" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Inventory Management
                </a>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="font-semibold text-white text-lg mb-6">Company</h4>
            <ul className="space-y-4">
              <li>
                <a href="/about" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  About Us
                </a>
              </li>
              <li>
                <a href="/careers" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Careers
                </a>
              </li>
              <li>
                <a href="/newsroom" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Newsroom
                </a>
              </li>
              <li>
                <a href="/contact" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Contact
                </a>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h4 className="font-semibold text-white text-lg mb-6">Resources</h4>
            <ul className="space-y-4">
              <li>
                <a href="/resources" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Articles
                </a>
              </li>
              <li>
                <a href="/resources" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Case Studies
                </a>
              </li>
              <li>
                <a href="/resources" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  Webinars
                </a>
              </li>
              <li>
                <a href="/how-it-works" className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300">
                  How It Works
                </a>
              </li>
            </ul>
          </div>

        </div>

        {/* Newsletter Section */}
        <div className="border-t border-gray-700 pt-12 mb-12">
          <div className="max-w-md mx-auto text-center">
            <h4 className="text-xl font-semibold text-white mb-4">Stay Updated</h4>
            <p className="text-gray-300 mb-6">Get the latest insights on warehouse management and supply chain optimization.</p>
            <div className="flex gap-3">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg bg-white/10 border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#E6B24B] focus:border-transparent"
              />
              <button className="px-6 py-3 bg-[#E6B24B] hover:bg-[#D4A843] text-black font-semibold rounded-lg transition-colors duration-300">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex items-center space-x-6">
            <p className="text-gray-400 text-sm">
              © 2024 Flexe. All rights reserved.
            </p>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <i className="fas fa-shield-alt text-[#4caf50]"></i>
              <span>SOC 2 Compliant</span>
            </div>
          </div>
          <div className="flex space-x-6">
            <a href="#" className="text-sm text-gray-400 hover:text-[#E6B24B] transition-colors duration-300">Privacy Policy</a>
            <a href="#" className="text-sm text-gray-400 hover:text-[#E6B24B] transition-colors duration-300">Terms of Service</a>
            <a href="#" className="text-sm text-gray-400 hover:text-[#E6B24B] transition-colors duration-300">Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  );
}
