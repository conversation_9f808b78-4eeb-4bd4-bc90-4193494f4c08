'use client';

import { useState } from 'react';
import Image from "next/image";
import Header from '../components/Header';
import Footer from '../components/Footer';

export default function SolutionsPage() {
  const [activeTab, setActiveTab] = useState('distribution');

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="solutions" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Modern warehouse with geometric patterns"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              {/* Badge */}
              <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full">
                <i className="fas fa-warehouse text-[#E6B24B] mr-3"></i>
                <span className="text-white font-semibold">Warehousing Solutions</span>
              </div>

              {/* Main Heading */}
              <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight">
                Flexible Warehousing Infrastructure allows enterprises to
                <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
                  evolve, optimize and prepare
                </span>
                networks.
              </h1>

              {/* Subtitle */}
              <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
                Partner with Flexe to dynamically add warehouses when, where and for as long as necessary.
              </p>

              {/* CTA Button */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] hover:from-[#D4A843] hover:to-[#E6B24B] text-black font-bold px-8 py-4 rounded-xl text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                  Get Started
                </button>
                <button className="border-2 border-white/20 text-white hover:bg-white/10 font-semibold px-8 py-4 rounded-xl text-lg transition-all duration-300">
                  Learn More
                </button>
              </div>
            </div>

            <div className="relative">
              <div className="relative overflow-hidden rounded-2xl">
                <img
                  alt="Abstract geometric warehouse design"
                  className="w-full h-96 object-cover"
                  src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Flexe Section */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              {/* Section Badge */}
              <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
                <i className="fas fa-star text-[#E6B24B] mr-3"></i>
                <span className="text-white font-semibold">Why Flexe</span>
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 leading-tight">
                Complement fixed networks with
                <span className="block text-[#4caf50]">tech-enabled warehouse services.</span>
              </h2>

              <div className="space-y-8">
                <div className="group flex items-start space-x-4 p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <i className="fas fa-expand-arrows-alt text-black"></i>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-[#E6B24B] transition-colors duration-300">
                      Scale with an asset-light and agile approach
                    </h3>
                    <p className="text-gray-300">
                      Complement fixed warehousing infrastructure with Flexible Warehousing Infrastructure. No CapEx, fixed-term agreements or fixed costs.
                    </p>
                  </div>
                </div>

                <div className="group flex items-start space-x-4 p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#4caf50] to-[#2e7d32] rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <i className="fas fa-network-wired text-black"></i>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-[#4caf50] transition-colors duration-300">
                      Expand, consolidate and optimize networks
                    </h3>
                    <p className="text-gray-300">
                      Transform supply chains fast with North America's largest flexible warehousing network of over <span className="text-[#E6B24B] font-semibold">700+ unique warehouse operators</span>.
                    </p>
                  </div>
                </div>

                <div className="group flex items-start space-x-4 p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <i className="fas fa-puzzle-piece text-black"></i>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-[#E6B24B] transition-colors duration-300">
                      Solve the most difficult supply chain problems—flexibly
                    </h3>
                    <p className="text-gray-300">
                      Improve delivery promise, reduce transportation costs, respond to supply chain disruptions or changes in demand and prevent production slowdowns—fast.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative overflow-hidden rounded-2xl">
                <img
                  alt="Modern warehouse operations"
                  className="w-full h-96 object-cover"
                  src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Solutions Tabs Section */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-cogs text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Our Solutions</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Choose Your
              <span className="block text-[#E6B24B]">Warehousing Solution</span>
            </h2>
          </div>

          {/* Tab Navigation */}
          <div className="flex justify-center mb-16">
            <div className="flex flex-wrap gap-4 bg-white/5 backdrop-blur-sm rounded-2xl p-2 border border-white/10">
              <button
                onClick={() => setActiveTab('distribution')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'distribution'
                    ? 'bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black shadow-lg'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                Distribution
              </button>
              <button
                onClick={() => setActiveTab('fulfillment')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'fulfillment'
                    ? 'bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black shadow-lg'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                Fulfillment
              </button>
              <button
                onClick={() => setActiveTab('capacity')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'capacity'
                    ? 'bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black shadow-lg'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                Capacity
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {activeTab === 'distribution' && (
              <>
                <div>
                  <h2 className="text-4xl font-bold text-white mb-6">
                    Get critical products to market. Quickly.
                  </h2>
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Improve store replenishment SLAs</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Enter/Expand into new markets and channels</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Consolidate and optimize networks</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">On/nearshoring</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Inventory build for seasonal products and peak demand</span>
                    </li>
                  </ul>
                  <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
                    Learn More
                  </button>
                </div>
                <div className="space-y-8">
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">124%</div>
                    <div className="text-white font-semibold mb-2">ROI when partnering with Flexe</div>
                    <div className="text-gray-400 text-sm">Forrester Research Total Economic Impact™ (TEI) Study of Flexe Logistics Programs, 2023</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">90%+</div>
                    <div className="text-white font-semibold mb-2">of consumers say they will buy another brand if their preferred choice isn't available</div>
                    <div className="text-gray-400 text-sm">The Wall Street Journal, Brand Loyalty Takes a Hit From Inflation, Shortages, 2022</div>
                  </div>
                </div>
              </>
            )}

            {activeTab === 'fulfillment' && (
              <>
                <div>
                  <h2 className="text-4xl font-bold text-white mb-6">
                    Unlock innovative customer experiences. Capture market share.
                  </h2>
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Faster eComm/Omnichannel fulfillment</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Reduce transportation costs</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Test new sales channels</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Launch new products fast</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Seasonal SKU optimization</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Optimize big and bulky SKUs</span>
                    </li>
                  </ul>
                  <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
                    Learn More
                  </button>
                </div>
                <div className="space-y-8">
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">21%</div>
                    <div className="text-white font-semibold mb-2">of consumers purchase a new product as soon as it comes out</div>
                    <div className="text-gray-400 text-sm">20+ Product Launch statistics you should know 2024, Learn.g2, 2024</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">83%</div>
                    <div className="text-white font-semibold mb-2">of consumers switch retailers for faster delivery</div>
                    <div className="text-gray-400 text-sm">The 2022 Omnichannel Retail Report.", Flexe Institute, Jun 06, 2022</div>
                  </div>
                </div>
              </>
            )}

            {activeTab === 'capacity' && (
              <>
                <div>
                  <h2 className="text-4xl font-bold text-white mb-6">
                    Storage when and where it's needed.
                  </h2>
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Disaster relief</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Forecast misses</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Manage excess inventory</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Opportunistic bulk buys</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Raw materials management</span>
                    </li>
                  </ul>
                  <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
                    Learn More
                  </button>
                </div>
                <div className="space-y-8">
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">50%+</div>
                    <div className="text-white font-semibold mb-2">reduction in new facility ramp time for Flexe customers</div>
                    <div className="text-gray-400 text-sm">Forrester Research Total Economic Impact™ (TEI) Study of Flexe Logistics Programs, 2023</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">$82M</div>
                    <div className="text-white font-semibold mb-2">Average yearly cost of global supply chain disruptions for large companies</div>
                    <div className="text-gray-400 text-sm">Reuters, 2023</div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Discover Flexible
              <span className="block text-[#4caf50]">Warehousing Infrastructure</span>
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Ready to transform your supply chain? Let's discuss how Flexe can help you scale efficiently.
            </p>
            <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] hover:from-[#D4A843] hover:to-[#E6B24B] text-black font-bold px-10 py-5 rounded-xl text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
              Contact Us Today
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
