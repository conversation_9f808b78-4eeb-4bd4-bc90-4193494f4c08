'use client';

import { useState, useEffect } from 'react';

export default function Header({ currentPage = '' }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [scrolled, setScrolled] = useState(false);

  const handleDropdownToggle = (dropdown) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const handleMouseEnter = (dropdown) => {
    setActiveDropdown(dropdown);
  };

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.classList.remove('mobile-menu-open');
    }

    return () => {
      document.body.classList.remove('mobile-menu-open');
    };
  }, [mobileMenuOpen]);

  return (
    <div className="relative">
      {/* Announcement Bar */}
      <div className="bg-gradient-to-r from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] text-white py-3 px-4">
        <div className="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between gap-2">
          <div className="flex items-center gap-3 text-sm font-medium">
            <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
              <i className="fas fa-rocket text-white text-xs"></i>
            </div>
            <span className="hidden sm:inline">🚀 New: Advanced Warehouse Management Solutions Available</span>
            <span className="sm:hidden">🚀 New Solutions Available</span>
          </div>
          <a href="#" className="text-sm font-semibold hover:text-white/80 transition-colors flex items-center gap-2 bg-white/10 px-4 py-2 rounded-full">
            Learn More
            <i className="fas fa-arrow-right text-xs"></i>
          </a>
        </div>
      </div>

      {/* Main Header */}
      <header className={`sticky top-0 z-50 transition-all duration-300 ${scrolled ? 'bg-white shadow-xl border-b border-[#0a1e3a]/10' : 'bg-white'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <a href="/" className="flex items-center space-x-4 group">
                <div className="w-12 h-12 lg:w-14 lg:h-14 rounded-xl bg-gradient-to-br from-[#E6B24B] to-[#D4A843] flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                  <i className="fas fa-network-wired text-black text-xl lg:text-2xl"></i>
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">
                    Flexe
                  </h1>
                  <p className="text-sm font-semibold text-[#0a1e3a]/80 hidden sm:block">Warehouse Management</p>
                </div>
              </a>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-2">
              {/* Solutions Dropdown */}
              <div
                className="relative group"
                onMouseEnter={() => handleMouseEnter('solutions')}
                onMouseLeave={handleMouseLeave}
              >
                <button className={`flex items-center space-x-2 px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${currentPage.includes('solutions') ? 'bg-gradient-to-r from-[#0a1e3a]/10 to-[#2a5a3a]/10 text-[#0a1e3a]' : 'text-[#0a1e3a] hover:bg-[#0a1e3a]/5'}`}>
                  <span>Solutions</span>
                  <i className="fas fa-chevron-down text-xs transition-transform duration-300 group-hover:rotate-180"></i>
                </button>

                {/* Dropdown Menu */}
                <div className={`absolute top-full left-0 mt-3 w-96 transition-all duration-300 ${activeDropdown === 'solutions' ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible translate-y-2'}`}>
                  <div className="bg-white rounded-2xl shadow-2xl border border-[#0a1e3a]/10 overflow-hidden backdrop-blur-lg">
                    <div className="p-8 grid gap-4">
                      {[
                        {
                          href: "/solutions",
                          icon: "warehouse",
                          title: "Warehousing",
                          desc: "Flexible infrastructure solutions",
                          gradient: "from-[#0a1e3a] to-[#2a5a3a]",
                        },
                        {
                          href: "/solutions/distribution",
                          icon: "truck",
                          title: "Distribution",
                          desc: "Rapid retail replenishment",
                          gradient: "from-[#0a1e3a] to-[#0a2e2a]",
                        },
                        {
                          href: "/solutions/fulfillment",
                          icon: "box",
                          title: "Fulfillment",
                          desc: "Optimized delivery networks",
                          gradient: "from-[#0a1e3a] to-[#2a5a3a]",
                        },
                      ].map((item, i) => (
                        <a
                          key={i}
                          href={item.href}
                          className="group flex items-start space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-[#0a1e3a]/5 hover:to-[#2a5a3a]/5 transition-all duration-300 border border-transparent hover:border-[#2a5a3a]/20"
                        >
                          <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${item.gradient} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                            <i className={`fas fa-${item.icon} text-white`}></i>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">{item.title}</h4>
                            <p className="text-sm mt-1 text-[#0a1e3a]/70">{item.desc}</p>
                          </div>
                        </a>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Static Pages */}
              {[
                { href: "/how-it-works", label: "How it Works" },
                { href: "/about", label: "About" },
                { href: "/resources", label: "Resources" },
                { href: "/contact", label: "Contact" },
              ].map((item, idx) => (
                <a
                  key={idx}
                  href={item.href}
                  className={`px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${
                    currentPage.includes(item.href.slice(1))
                      ? 'bg-gradient-to-r from-[#0a1e3a]/10 to-[#2a5a3a]/10 text-[#0a1e3a]'
                      : 'text-[#0a1e3a] hover:bg-[#0a1e3a]/5'
                  }`}
                >
                  {item.label}
                </a>
              ))}
            </nav>

            {/* Right Side */}
            <div className="flex items-center space-x-4">
              <a
                href="https://app.flexe.com/users/sign_in"
                target="_blank"
                rel="noopener noreferrer"
                className="hidden sm:flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-[#0a1e3a]/70 hover:text-[#0a1e3a] transition-colors duration-300 rounded-xl hover:bg-[#0a1e3a]/5"
              >
                <i className="fas fa-sign-in-alt text-sm"></i>
                <span>Sign In</span>
              </a>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="lg:hidden text-[#0a1e3a] focus:outline-none"
              >
                <i className="fas fa-bars text-xl"></i>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden bg-white border-t border-gray-200 px-4 py-4 space-y-4">
            {[
              { href: "/solutions", label: "Solutions" },
              { href: "/how-it-works", label: "How it Works" },
              { href: "/about", label: "About" },
              { href: "/resources", label: "Resources" },
              { href: "/contact", label: "Contact" },
              { href: "https://app.flexe.com/users/sign_in", label: "Sign In", external: true },
            ].map((item, idx) => (
              <a
                key={idx}
                href={item.href}
                target={item.external ? "_blank" : "_self"}
                rel={item.external ? "noopener noreferrer" : ""}
                className="block text-sm font-semibold text-[#0a1e3a] hover:text-[#2a5a3a] transition-colors"
              >
                {item.label}
              </a>
            ))}
          </div>
        )}
      </header>
    </div>
  );
}
