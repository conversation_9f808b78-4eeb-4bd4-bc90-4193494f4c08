'use client';

import { useState, useEffect } from 'react';

export default function Header({ currentPage = '' }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [scrolled, setScrolled] = useState(false);

  const handleDropdownToggle = (dropdown) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const handleMouseEnter = (dropdown) => {
    setActiveDropdown(dropdown);
  };

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.classList.remove('mobile-menu-open');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('mobile-menu-open');
    };
  }, [mobileMenuOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [mobileMenuOpen]);

  return (
    <div className="relative">
      {/* Enhanced Top Announcement Bar with Navy-Green Theme */}
      <div className="bg-gradient-to-r from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] text-white py-3 px-4">
        <div className="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between gap-2">
          <div className="flex items-center gap-3 text-sm font-medium">
            <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
              <i className="fas fa-rocket text-white text-xs"></i>
            </div>
            <span className="hidden sm:inline">🚀 New: Advanced Warehouse Management Solutions Available</span>
            <span className="sm:hidden">🚀 New Solutions Available</span>
          </div>
          <a href="#" className="text-sm font-semibold hover:text-white/80 transition-colors flex items-center gap-2 bg-white/10 px-4 py-2 rounded-full">
            Learn More
            <i className="fas fa-arrow-right text-xs"></i>
          </a>
        </div>
      </div>

      {/* Enhanced Main Header with Navy-Green Theme - Fixed Background */}
      <header className={`sticky top-0 z-50 transition-all duration-300 ${scrolled ? 'bg-white shadow-xl border-b border-[#0a1e3a]/10' : 'bg-white'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">

            {/* Fixed Logo Section - Enhanced Visibility */}
            <div className="flex items-center space-x-3">
              <a href="/" className="flex items-center space-x-4 group">
                <div className="w-12 h-12 lg:w-14 lg:h-14 rounded-xl bg-gradient-to-br from-[#0a1e3a] to-[#2a5a3a] flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                  <i className="fas fa-warehouse text-white text-xl lg:text-2xl"></i>
                </div>
                <div>
                  <h1 className="header-logo text-2xl lg:text-3xl font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">
                    Flexe
                  </h1>
                  <p className="text-sm font-semibold text-[#0a1e3a]/80 hidden sm:block">Warehouse Management</p>
                </div>
              </a>
            </div>

            {/* Enhanced Navigation with Navy-Green Theme */}
            <nav className="hidden lg:flex items-center space-x-2">
              {/* Enhanced Solutions Dropdown */}
              <div
                className="relative group"
                onMouseEnter={() => handleMouseEnter('solutions')}
                onMouseLeave={handleMouseLeave}
              >
                <button className={`flex items-center space-x-2 px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${currentPage.includes('solutions') ? 'bg-gradient-to-r from-[#0a1e3a]/10 to-[#2a5a3a]/10 text-[#0a1e3a]' : 'text-[#0a1e3a] hover:bg-[#0a1e3a]/5'}`}>
                  <span>Solutions</span>
                  <i className="fas fa-chevron-down text-xs transition-transform duration-300 group-hover:rotate-180"></i>
                </button>

                {/* Enhanced Dropdown */}
                <div className={`absolute top-full left-0 mt-3 w-96 z-[60] transition-all duration-300 ${activeDropdown === 'solutions' ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible translate-y-2'}`}>
                  <div className="bg-white rounded-2xl shadow-2xl border border-[#0a1e3a]/10 overflow-hidden backdrop-blur-lg">
                    <div className="p-8">
                      <div className="grid gap-4">
                        <a href="/solutions" className="group flex items-start space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-[#0a1e3a]/5 hover:to-[#2a5a3a]/5 transition-all duration-300 border border-transparent hover:border-[#2a5a3a]/20">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[#0a1e3a] to-[#2a5a3a] flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                            <i className="fas fa-warehouse text-white"></i>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">Warehousing</h4>
                            <p className="text-sm mt-1 text-[#0a1e3a]/70">Flexible infrastructure solutions</p>
                          </div>
                        </a>
                        <a href="/solutions/distribution" className="group flex items-start space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-[#0a1e3a]/5 hover:to-[#0a2e2a]/5 transition-all duration-300 border border-transparent hover:border-[#0a2e2a]/20">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[#0a1e3a] to-[#0a2e2a] flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                            <i className="fas fa-truck text-white"></i>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold text-[#0a1e3a] group-hover:text-[#0a2e2a] transition-colors duration-300">Distribution</h4>
                            <p className="text-sm mt-1 text-[#0a1e3a]/70">Rapid retail replenishment</p>
                          </div>
                        </a>
                        <a href="/solutions/fulfillment" className="group flex items-start space-x-4 p-4 rounded-xl hover:bg-gradient-to-r hover:from-[#0a1e3a]/5 hover:to-[#2a5a3a]/5 transition-all duration-300 border border-transparent hover:border-[#2a5a3a]/20">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[#0a1e3a] to-[#2a5a3a] flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                            <i className="fas fa-box text-white"></i>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">Fulfillment</h4>
                            <p className="text-sm mt-1 text-[#0a1e3a]/70">Optimized delivery networks</p>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Navigation Links */}
              <a href="/how-it-works" className={`px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${currentPage.includes('how-it-works') ? 'bg-gradient-to-r from-[#0a1e3a]/10 to-[#2a5a3a]/10 text-[#0a1e3a]' : 'text-[#0a1e3a] hover:bg-[#0a1e3a]/5'}`}>
                How it Works
              </a>

              <a href="/about" className={`px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${currentPage.includes('about') ? 'bg-gradient-to-r from-[#0a1e3a]/10 to-[#2a5a3a]/10 text-[#0a1e3a]' : 'text-[#0a1e3a] hover:bg-[#0a1e3a]/5'}`}>
                About
              </a>

              <a href="/resources" className={`px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${currentPage.includes('resources') ? 'bg-gradient-to-r from-[#0a1e3a]/10 to-[#2a5a3a]/10 text-[#0a1e3a]' : 'text-[#0a1e3a] hover:bg-[#0a1e3a]/5'}`}>
                Resources
              </a>

              <a href="/contact" className={`px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${currentPage.includes('contact') ? 'bg-gradient-to-r from-[#0a1e3a]/10 to-[#2a5a3a]/10 text-[#0a1e3a]' : 'text-[#0a1e3a] hover:bg-[#0a1e3a]/5'}`}>
                Contact
              </a>

            </nav>

            {/* Enhanced Action Buttons */}
            <div className="flex items-center space-x-4">
              <a href="https://app.flexe.com/users/sign_in" target="_blank" rel="noopener noreferrer" className="hidden sm:flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-[#0a1e3a]/70 hover:text-[#0a1e3a] transition-colors duration-300 rounded-xl hover:bg-[#0a1e3a]/5">
                <i className="fas fa-sign-in-alt text-sm"></i>
                <span>Sign In</span>
              </a>

              <a href="/contact" className="header-contact-btn hidden sm:flex items-center space-x-2 px-6 py-3 text-white text-sm font-bold rounded-xl bg-gradient-to-r from-[#0a1e3a] to-[#2a5a3a] hover:from-[#2a5a3a] hover:to-[#0a1e3a] transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-[#0a1e3a]/20">
                <span>Contact Us</span>
                <i className="fas fa-arrow-right text-sm"></i>
              </a>

              {/* Enhanced Mobile menu button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="mobile-menu-btn lg:hidden p-3 transition-all duration-300 relative z-[60] rounded-xl hover:bg-[#0a1e3a]/5 border border-[#0a1e3a]/10"
                aria-label="Toggle mobile menu"
              >
                <div className="w-6 h-6 flex flex-col justify-center items-center">
                  <span className={`block w-6 h-0.5 bg-[#0a1e3a] transition-all duration-300 ${mobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></span>
                  <span className={`block w-6 h-0.5 bg-[#0a1e3a] transition-all duration-300 mt-1 ${mobileMenuOpen ? 'opacity-0' : ''}`}></span>
                  <span className={`block w-6 h-0.5 bg-[#0a1e3a] transition-all duration-300 mt-1 ${mobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></span>
                </div>
              </button>
            </div>
          </div>

          {/* Enhanced Mobile Menu with Navy-Green Theme */}
          <div className={`lg:hidden fixed inset-0 z-[55] transition-all duration-300 ${mobileMenuOpen ? 'visible opacity-100' : 'invisible opacity-0'}`}>
            {/* Enhanced Backdrop */}
            <div
              className="absolute inset-0 bg-[#0a1e3a]/90 backdrop-blur-lg"
              onClick={() => setMobileMenuOpen(false)}
            ></div>

            {/* Enhanced Menu Panel */}
            <div className={`absolute top-0 right-0 h-full w-80 max-w-[90vw] shadow-2xl transform transition-all duration-300 ease-in-out bg-gradient-to-b from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] ${mobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`}>

              {/* Enhanced Header */}
              <div className="flex items-center justify-between p-6 border-b border-white/10">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[#4caf50] to-[#2e7d32] flex items-center justify-center shadow-lg">
                    <i className="fas fa-warehouse text-white text-lg"></i>
                  </div>
                  <div>
                    <h2 className="font-bold text-white text-lg">Flexe</h2>
                    <p className="text-sm text-white/70">Warehouse Management</p>
                  </div>
                </div>
                <button
                  onClick={() => setMobileMenuOpen(false)}
                  className="p-2 hover:bg-white/10 rounded-xl transition-colors text-white/70 hover:text-white z-[60]"
                  aria-label="Close mobile menu"
                >
                  <i className="fas fa-times text-xl"></i>
                </button>
              </div>

              {/* Enhanced Navigation */}
              <nav className="flex-1 p-6 overflow-y-auto">
                <div className="space-y-3">
                  <a href="/solutions" className="flex items-center space-x-4 p-4 text-white/80 hover:text-white rounded-xl hover:bg-white/10 transition-all duration-300 group">
                    <div className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors duration-300">
                      <i className="fas fa-warehouse"></i>
                    </div>
                    <span className="font-semibold">Solutions</span>
                  </a>
                  <a href="/how-it-works" className="flex items-center space-x-4 p-4 text-white/80 hover:text-white rounded-xl hover:bg-white/10 transition-all duration-300 group">
                    <div className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors duration-300">
                      <i className="fas fa-cogs"></i>
                    </div>
                    <span className="font-semibold">How It Works</span>
                  </a>
                  <a href="/about" className="flex items-center space-x-4 p-4 text-white/80 hover:text-white rounded-xl hover:bg-white/10 transition-all duration-300 group">
                    <div className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors duration-300">
                      <i className="fas fa-building"></i>
                    </div>
                    <span className="font-semibold">About</span>
                  </a>
                  <a href="/resources" className="flex items-center space-x-4 p-4 text-white/80 hover:text-white rounded-xl hover:bg-white/10 transition-all duration-300 group">
                    <div className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors duration-300">
                      <i className="fas fa-book"></i>
                    </div>
                    <span className="font-semibold">Resources</span>
                  </a>
                  <a href="/contact" className="flex items-center space-x-4 p-4 text-white/80 hover:text-white rounded-xl hover:bg-white/10 transition-all duration-300 group">
                    <div className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center group-hover:bg-white/20 transition-colors duration-300">
                      <i className="fas fa-envelope"></i>
                    </div>
                    <span className="font-semibold">Contact</span>
                  </a>
                </div>

                {/* Enhanced CTA Buttons */}
                <div className="mt-8 space-y-4">
                  <a href="/contact" className="block w-full px-6 py-4 bg-white text-[#0a1e3a] text-center font-bold rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-lg">
                    Contact Us
                  </a>
                  <a href="https://app.flexe.com/users/sign_in" target="_blank" rel="noopener noreferrer" className="block w-full px-6 py-4 border-2 border-white/30 text-white text-center font-semibold rounded-xl hover:bg-white/10 hover:border-white/50 transition-all duration-300">
                    Sign In
                  </a>
                </div>
              </nav>
            </div>
          </div>
        </div>
      </header>
    </div>
  );
}
