import Header from '../components/Header';
import Footer from '../components/Footer';

export default function NewsroomPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="newsroom" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Modern newsroom and media center"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-newspaper text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Newsroom</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight mb-8">
              Latest News &
              <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
                Company Updates
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Stay up to date with the latest developments, partnerships, and innovations from Flexe as we continue to transform the supply chain industry.
            </p>
          </div>
        </div>
      </section>

      {/* Featured News */}
      <section className="relative py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-16">
            {/* Section Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-star text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Featured News</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Latest
              <span className="block text-[#4caf50]">Headlines</span>
            </h2>
          </div>

          {/* Featured Article */}
          <div className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:bg-white/10 transition-all duration-300 mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
              <div className="relative">
                <img 
                  alt="Flexe announces major partnership"
                  className="w-full h-64 lg:h-full object-cover"
                  src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
              </div>
              <div className="p-8 lg:p-12">
                <div className="flex items-center mb-4">
                  <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase mr-3">
                    Press Release
                  </span>
                  <span className="text-gray-400 text-sm">December 15, 2024</span>
                </div>
                <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
                  Flexe Announces Strategic Partnership with Major Retail Chain
                </h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Flexe today announced a strategic partnership that will expand flexible warehousing infrastructure across key markets, enabling faster delivery times and improved customer satisfaction.
                </p>
                <a href="#" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                  Read Full Story <i className="fas fa-arrow-right ml-2"></i>
                </a>
              </div>
            </div>
          </div>

          {/* News Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* News Item */}
            <div className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:bg-white/10 transition-all duration-300">
              <div className="relative">
                <img 
                  alt="Flexe funding announcement"
                  className="w-full h-48 object-cover"
                  src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                    Funding
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="text-gray-400 text-sm mb-3">December 10, 2024</div>
                <h3 className="text-lg font-bold text-white mb-3">
                  Flexe Raises $50M Series C to Accelerate Growth
                </h3>
                <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                  New funding will support expansion of flexible warehousing network and technology platform enhancements.
                </p>
                <a href="#" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 text-sm">
                  Read More <i className="fas fa-arrow-right ml-1"></i>
                </a>
              </div>
            </div>

            {/* News Item */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="relative">
                <img 
                  alt="Flexe award recognition"
                  className="w-full h-48 object-cover"
                  src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                    Award
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="text-gray-400 text-sm mb-3">December 5, 2024</div>
                <h3 className="text-lg font-bold text-white mb-3">
                  Flexe Named "Supply Chain Innovation Leader"
                </h3>
                <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                  Industry recognition for pioneering flexible warehousing infrastructure solutions.
                </p>
                <a href="#" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 text-sm">
                  Read More <i className="fas fa-arrow-right ml-1"></i>
                </a>
              </div>
            </div>

            {/* News Item */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="relative">
                <img 
                  alt="Flexe product launch"
                  className="w-full h-48 object-cover"
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                    Product
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="text-gray-400 text-sm mb-3">November 28, 2024</div>
                <h3 className="text-lg font-bold text-white mb-3">
                  New AI-Powered Analytics Platform Launched
                </h3>
                <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                  Advanced data intelligence capabilities help enterprises optimize their supply chain networks.
                </p>
                <a href="#" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 text-sm">
                  Read More <i className="fas fa-arrow-right ml-1"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Press Kit */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Press Kit & Media Resources
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Download logos, images, and company information for media use
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-image text-black text-2xl"></i>
              </div>
              <h3 className="text-lg font-bold text-white mb-4">Company Logos</h3>
              <p className="text-gray-300 text-sm mb-6">High-resolution logos in various formats</p>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                Download <i className="fas fa-download ml-2"></i>
              </button>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-file-alt text-black text-2xl"></i>
              </div>
              <h3 className="text-lg font-bold text-white mb-4">Fact Sheet</h3>
              <p className="text-gray-300 text-sm mb-6">Key company facts and statistics</p>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                Download <i className="fas fa-download ml-2"></i>
              </button>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-users text-black text-2xl"></i>
              </div>
              <h3 className="text-lg font-bold text-white mb-4">Executive Bios</h3>
              <p className="text-gray-300 text-sm mb-6">Leadership team biographies</p>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                Download <i className="fas fa-download ml-2"></i>
              </button>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-camera text-black text-2xl"></i>
              </div>
              <h3 className="text-lg font-bold text-white mb-4">Product Images</h3>
              <p className="text-gray-300 text-sm mb-6">Screenshots and product visuals</p>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                Download <i className="fas fa-download ml-2"></i>
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Media Contact */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Media Inquiries
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            For press inquiries, interviews, or additional information, please contact our media relations team.
          </p>
          
          <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 max-w-md mx-auto">
            <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
              <i className="fas fa-envelope text-black text-2xl"></i>
            </div>
            <h3 className="text-xl font-bold text-white mb-4">Press Contact</h3>
            <p className="text-gray-300 mb-2">Media Relations Team</p>
            <a href="mailto:<EMAIL>" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
              <EMAIL>
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
