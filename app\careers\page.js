import Header from '../components/Header';
import Footer from '../components/Footer';

export default function CareersPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
      <Header currentPage="careers" />

      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            alt="Modern office and team collaboration"
            className="w-full h-full object-cover opacity-10"
            src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a1e3a]/90 via-[#0a2e2a]/90 to-[#2a5a3a]/90"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full mb-8">
              <i className="fas fa-users text-[#E6B24B] mr-3"></i>
              <span className="text-white font-semibold">Join Our Team</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight mb-8">
              Build the Future of
              <span className="block bg-gradient-to-r from-[#E6B24B] to-[#4caf50] bg-clip-text text-transparent">
                Supply Chain Technology
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Join a team of passionate professionals who are revolutionizing how enterprises manage their supply chains through flexible warehousing infrastructure.
            </p>
          </div>
        </div>
      </section>

      {/* Why Work at Flexe */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Why Work at Flexe?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We're building the future of logistics technology while creating an environment where talented people can thrive.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-rocket text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Innovation First</h3>
              <p className="text-gray-300 leading-relaxed">
                Work on cutting-edge technology that's transforming how Fortune 500 companies manage their supply chains.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-chart-line text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Growth Opportunities</h3>
              <p className="text-gray-300 leading-relaxed">
                Advance your career in a fast-growing company with opportunities to learn, lead, and make a real impact.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-heart text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Great Benefits</h3>
              <p className="text-gray-300 leading-relaxed">
                Comprehensive health coverage, competitive compensation, flexible work arrangements, and more.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-globe text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Remote Friendly</h3>
              <p className="text-gray-300 leading-relaxed">
                Work from anywhere with our flexible remote work policies and collaborative digital-first culture.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-handshake text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Collaborative Culture</h3>
              <p className="text-gray-300 leading-relaxed">
                Join a team that values collaboration, diversity, and creating an inclusive environment for everyone.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-graduation-cap text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Learning & Development</h3>
              <p className="text-gray-300 leading-relaxed">
                Continuous learning opportunities, conference attendance, and professional development support.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Open Positions
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Explore current opportunities to join our growing team
            </p>
          </div>

          <div className="space-y-6">
            {/* Job Listing */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-xl font-bold text-white mb-2">Senior Software Engineer</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-300">
                    <span className="flex items-center"><i className="fas fa-map-marker-alt text-[#E6B24B] mr-2"></i>Seattle, WA / Remote</span>
                    <span className="flex items-center"><i className="fas fa-briefcase text-[#E6B24B] mr-2"></i>Full-time</span>
                    <span className="flex items-center"><i className="fas fa-layer-group text-[#E6B24B] mr-2"></i>Engineering</span>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-6 py-3 rounded-xl hover:scale-105 transition-transform duration-300">
                  Apply Now
                </button>
              </div>
            </div>

            {/* Job Listing */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-xl font-bold text-white mb-2">Product Manager</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-300">
                    <span className="flex items-center"><i className="fas fa-map-marker-alt text-[#E6B24B] mr-2"></i>Seattle, WA / Remote</span>
                    <span className="flex items-center"><i className="fas fa-briefcase text-[#E6B24B] mr-2"></i>Full-time</span>
                    <span className="flex items-center"><i className="fas fa-layer-group text-[#E6B24B] mr-2"></i>Product</span>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-6 py-3 rounded-xl hover:scale-105 transition-transform duration-300">
                  Apply Now
                </button>
              </div>
            </div>

            {/* Job Listing */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-xl font-bold text-white mb-2">Logistics Analyst</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-300">
                    <span className="flex items-center"><i className="fas fa-map-marker-alt text-[#E6B24B] mr-2"></i>Seattle, WA</span>
                    <span className="flex items-center"><i className="fas fa-briefcase text-[#E6B24B] mr-2"></i>Full-time</span>
                    <span className="flex items-center"><i className="fas fa-layer-group text-[#E6B24B] mr-2"></i>Operations</span>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-6 py-3 rounded-xl hover:scale-105 transition-transform duration-300">
                  Apply Now
                </button>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-300 mb-6">Don't see a position that fits? We're always looking for talented people.</p>
            <a href="/contact" className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-8 py-4 rounded-xl transition-all duration-300">
              Send Us Your Resume
            </a>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Ready to Join Our Team?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Be part of the team that's transforming supply chain technology and helping enterprises achieve their logistics goals.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
              Get In Touch
            </a>
            <a href="/about" className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-8 py-4 rounded-xl transition-all duration-300">
              Learn About Flexe
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
