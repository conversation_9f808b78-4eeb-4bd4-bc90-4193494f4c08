@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import "tailwindcss";

/* PROFESSIONAL MODERN STYLES */
body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
}

/* Color Variables - EXACT FROM INDEX.HTML */
:root {
  --primary-bg: #e3e8ef;
  --primary-text: #0a1e3a;
  --secondary-text: #7a7a7a;
  --accent-orange: #f97316;
  --success-green: #4caf50;
  --success-green-hover: #3a8e3a;
  --dark-gradient-start: #0a1e3a;
  --dark-gradient-mid: #0a2e2a;
  --dark-gradient-end: #2a5a3a;
}

/* subtle shadow for cards - EXACT FROM INDEX.HTML */
.card-shadow {
  box-shadow: 0 12px 24px rgba(0,0,0,0.25);
}

/* subtle text shadow for headings - EXACT FROM INDEX.HTML */
h1, h2, h3, h4 {
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* Color utility classes for fallback */
.bg-primary-bg { background-color: var(--primary-bg); }
.bg-primary-text { background-color: var(--primary-text); }
.bg-secondary-text { background-color: var(--secondary-text); }
.bg-accent-orange { background-color: var(--accent-orange); }
.bg-success-green { background-color: var(--success-green); }
.bg-success-green-hover { background-color: var(--success-green-hover); }
.bg-dark-gradient-start { background-color: var(--dark-gradient-start); }
.bg-dark-gradient-mid { background-color: var(--dark-gradient-mid); }
.bg-dark-gradient-end { background-color: var(--dark-gradient-end); }

.text-primary-text { color: var(--primary-text); }
.text-secondary-text { color: var(--secondary-text); }
.text-accent-orange { color: var(--accent-orange); }
.text-success-green { color: var(--success-green); }

/* PROFESSIONAL ELEGANT ANIMATIONS */

/* Fade In Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Slide In Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Bounce Animations */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceX {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(0);
  }
  40% {
    transform: translateX(3px);
  }
  60% {
    transform: translateX(-3px);
  }
}

/* Count Up Animation */
@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse Subtle Animation */
@keyframes pulseSubtle {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
  opacity: 0;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
  opacity: 0;
}

.animate-bounce-x {
  animation: bounceX 2s infinite;
}

.animate-count-up {
  animation: countUp 1s ease-out forwards;
  opacity: 0;
}

.animate-pulse-subtle {
  animation: pulseSubtle 3s ease-in-out infinite;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Text Glow Effect */
.text-glow {
  text-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

/* SCROLL-TRIGGERED ANIMATIONS */

/* Base state for scroll animations - hidden by default */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Activated state when element comes into view */
.scroll-animate.animate-in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Different animation types for scroll */
.scroll-fade-in-left {
  transform: translateX(-50px);
}

.scroll-fade-in-left.animate-in-view {
  transform: translateX(0);
}

.scroll-fade-in-right {
  transform: translateX(50px);
}

.scroll-fade-in-right.animate-in-view {
  transform: translateX(0);
}

.scroll-fade-in-up {
  transform: translateY(50px);
}

.scroll-fade-in-up.animate-in-view {
  transform: translateY(0);
}

.scroll-slide-in-right {
  transform: translateX(100px);
}

.scroll-slide-in-right.animate-in-view {
  transform: translateX(0);
}

.scroll-bounce-in {
  transform: scale(0.3) translateY(30px);
}

.scroll-bounce-in.animate-in-view {
  transform: scale(1) translateY(0);
  transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.scroll-zoom-in {
  transform: scale(0.8);
}

.scroll-zoom-in.animate-in-view {
  transform: scale(1);
}

.scroll-rotate-in {
  transform: rotate(-10deg) scale(0.8);
}

.scroll-rotate-in.animate-in-view {
  transform: rotate(0deg) scale(1);
}

/* Staggered delays for scroll animations */
.scroll-animate[data-delay="0"] { transition-delay: 0ms; }
.scroll-animate[data-delay="100"] { transition-delay: 100ms; }
.scroll-animate[data-delay="200"] { transition-delay: 200ms; }
.scroll-animate[data-delay="300"] { transition-delay: 300ms; }
.scroll-animate[data-delay="400"] { transition-delay: 400ms; }
.scroll-animate[data-delay="500"] { transition-delay: 500ms; }
.scroll-animate[data-delay="600"] { transition-delay: 600ms; }

/* Pulse dot animation for orange dots */
@keyframes pulseDot {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 6px rgba(249, 115, 22, 0.5);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(249, 115, 22, 0.7);
  }
}

.animate-pulse-dot {
  animation: pulseDot 2s ease-in-out infinite;
}

/* Professional hover effects for scroll elements */
.scroll-animate.animate-in-view:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}

/* Text reveal animation */
@keyframes textReveal {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scroll-text-reveal {
  opacity: 0;
}

.scroll-text-reveal.animate-in-view {
  animation: textReveal 0.8s ease-out forwards;
}

/* Additional Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

/* Glassmorphism effect for dark theme */
.glass {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Custom gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #E6B24B 0%, #D4A843 50%, #C19A3B 100%);
}

.warehouse-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(230, 178, 75, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(230, 178, 75, 0.05) 0%, transparent 50%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.02) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.02) 25%, transparent 25%);
  background-size: 60px 60px, 80px 80px, 20px 20px, 20px 20px;
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(230, 178, 75, 0.6);
}

/* Dark hover effects for images */
.hover-dark:hover {
  filter: brightness(0.7) contrast(1.2);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

/* Enhanced dark theme elements */
.dark-card {
  background: linear-gradient(145deg, rgba(0, 0, 0, 0.6), rgba(30, 30, 30, 0.4));
  border: 1px solid rgba(230, 178, 75, 0.2);
}

.dark-section {
  background: linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 50%, #1a1a1a 100%);
}

/* Professional button styles */
.btn-primary {
  background: linear-gradient(135deg, #E6B24B 0%, #D4A843 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(230, 178, 75, 0.4);
}

/* Light theme specific styles */
.text-primary {
  color: #1e293b;
}

.text-secondary {
  color: #64748b;
}

.bg-light {
  background-color: #ffffff;
}

.border-light {
  border-color: rgba(0, 0, 0, 0.1);
}

/* CLEAN MOBILE NAVIGATION */
@media (max-width: 1023px) {
  /* Prevent body scroll when mobile menu is open */
  body.mobile-menu-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  /* Clean mobile header - solid background */
  .glass {
    background: rgba(0, 0, 0, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
  }

  /* Professional mobile menu styling */
  .mobile-menu {
    background: #1a1a1a;
    border-left: 3px solid #E6B24B;
    box-shadow: -20px 0 60px rgba(0, 0, 0, 0.8);
  }

  .mobile-menu-header {
    background: #111111 !important;
    border-bottom: 3px solid #E6B24B !important;
    padding: 24px !important;
  }

  .mobile-logo-icon {
    background: linear-gradient(135deg, #E6B24B 0%, #D4A843 100%) !important;
    width: 48px !important;
    height: 48px !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 4px 12px rgba(230, 178, 75, 0.3) !important;
  }

  .mobile-logo-icon i {
    color: #000000 !important;
    font-size: 20px !important;
    font-weight: bold !important;
  }

  .mobile-menu-nav {
    background: #1a1a1a;
  }

  .mobile-menu-link {
    color: #ffffff;
    padding: 16px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .mobile-menu-link:hover {
    background: #E6B24B;
    color: #000000;
    transform: translateX(8px);
  }

  .mobile-menu-link i {
    color: #E6B24B;
    width: 20px;
    margin-right: 16px;
    transition: color 0.3s ease;
  }

  .mobile-menu-link:hover i {
    color: #000000;
  }

  .mobile-cta-primary {
    background: linear-gradient(135deg, #E6B24B 0%, #D4A843 100%);
    color: #000000;
    font-weight: bold;
    padding: 16px 24px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
  }

  .mobile-cta-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(230, 178, 75, 0.4);
  }

  .mobile-cta-secondary {
    border: 2px solid #E6B24B;
    color: #E6B24B;
    font-weight: 600;
    padding: 14px 24px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
  }

  .mobile-cta-secondary:hover {
    background: #E6B24B;
    color: #000000;
  }
}

/* MODERN ANIMATIONS FOR HOME PAGE */

/* Fade In Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(60px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 1s ease-out forwards;
}

.animate-fade-in-up {
  animation: fadeInUp 1.2s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 1.2s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Gradient Text Animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Modern Card Hover Effects */
.modern-card {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-style: preserve-3d;
}

.modern-card:hover {
  transform: translateY(-12px) rotateX(5deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Glass Morphism Enhanced */
.glass-enhanced {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Smooth Scroll */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #E6B24B, #D4A843);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #F4C430, #E6B24B);
}

/* Header Visibility Fixes */
.header-fixed {
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Mobile Menu Fixes */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 55;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.mobile-menu-panel {
  position: absolute;
  top: 0;
  right: 0;
  height: 100vh;
  width: 320px;
  max-width: 90vw;
  z-index: 56;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.mobile-menu-panel.open {
  transform: translateX(0);
}

/* Dropdown Menu Fixes */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 60;
  min-width: 384px;
  margin-top: 12px;
}

/* Ensure proper stacking context */
.nav-dropdown {
  position: relative;
  z-index: 10;
}

/* Header Element Visibility Fixes */
.header-logo {
  color: #0a1e3a !important;
  font-weight: bold !important;
}

.header-logo:hover {
  color: #2a5a3a !important;
}

.header-contact-btn {
  background: linear-gradient(135deg, #0a1e3a 0%, #2a5a3a 100%) !important;
  color: white !important;
  border: 1px solid rgba(10, 30, 58, 0.2) !important;
  box-shadow: 0 4px 12px rgba(10, 30, 58, 0.3) !important;
}

.header-contact-btn:hover {
  background: linear-gradient(135deg, #2a5a3a 0%, #0a1e3a 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(10, 30, 58, 0.4) !important;
}

.mobile-menu-btn {
  border: 1px solid rgba(10, 30, 58, 0.1) !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

.mobile-menu-btn:hover {
  background: rgba(10, 30, 58, 0.05) !important;
}

.mobile-menu-btn span {
  background-color: #0a1e3a !important;
}
